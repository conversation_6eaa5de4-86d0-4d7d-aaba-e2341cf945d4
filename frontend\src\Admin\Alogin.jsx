import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import axios from "axios";

// Custom hook for form validation
const useFormValidation = (email, password) => {
  const [errors, setErrors] = useState({});

  useEffect(() => {
    const newErrors = {};
    // Email validation
    if (email && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      newErrors.email = "Invalid email address";
    }
    // Password validation
    if (password && password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long";
    }
    setErrors(newErrors);
  }, [email, password]);

  return errors;
};

const AdminLogin = () => {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("admin123");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const navigate = useNavigate();

  // Check if admin is already logged in
  useEffect(() => {
    const admin = localStorage.getItem('admin');
    if (admin) {
      setIsLoggedIn(true);
      setCurrentUser(JSON.parse(admin));
    }
  }, []);



  const validationErrors = useFormValidation(email, password);

  // Configure axios defaults
  useEffect(() => {
    axios.defaults.withCredentials = true;
  }, []);

  // Debounced form submission
  const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    // Validation check
    if (Object.keys(validationErrors).length > 0) {
      setError("Please fix the form errors before submitting");
      setLoading(false);
      return;
    }

    if (!email.trim() || !password.trim()) {
      setError("Please fill in all fields");
      setLoading(false);
      return;
    }

    try {
      const payload = { email: email.trim(), password };
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL || "http://localhost:4000"}/alogin`,
        payload,
        { withCredentials: true }
      );

      if (response.data.Status === "Success") {
        localStorage.setItem("user", JSON.stringify(response.data.user));
        navigate("/ahome");
      } else {
        setError(response.data || "Invalid credentials");
      }
    } catch (err) {
      console.error("Admin login error:", err);
      setError(
        err.response?.data?.message ||
        "Login failed. Please check your connection and try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const debouncedHandleSubmit = debounce(handleSubmit, 300);

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/asignup");
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-indigo-100 to-purple-100" role="main">
      <header className="w-full py-6 bg-indigo-600 shadow-md mb-8">
        <div className="container mx-auto flex items-center justify-between px-4">
          <h1 className="text-2xl font-bold text-white cursor-pointer" onClick={() => navigate("/")}>
            BookStore Admin
          </h1>
          <nav className="flex items-center space-x-4">
            <button
              className="text-white font-semibold hover:underline"
              onClick={() => navigate("/")}
              aria-label="Go to home page"
            >
              Home
            </button>
            {isLoggedIn ? (
              <div className="flex items-center space-x-4">
                <span className="text-white text-sm">
                  Welcome, {currentUser?.name || 'Admin'}
                </span>
              </div>
            ) : (
              <button
                className="text-white font-semibold hover:underline"
                onClick={handleSignup}
                aria-label="Go to admin signup page"
              >
                Admin Signup
              </button>
            )}
          </nav>
        </div>
      </header>
      
      <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-2">Admin Login</h2>
          <p className="text-gray-600">Access the admin dashboard</p>
        </div>

        {error && (
          <div 
            className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"
            role="alert"
            aria-live="polite"
          >
            {error}
          </div>
        )}

        <form onSubmit={debouncedHandleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              placeholder="Enter your admin email"
              aria-describedby="email-error"
              required
            />
            {validationErrors.email && (
              <p className="mt-1 text-sm text-red-600" id="email-error">
                {validationErrors.email}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              placeholder="Enter your password"
              aria-describedby="password-error"
              required
            />
            {validationErrors.password && (
              <p className="mt-1 text-sm text-red-600" id="password-error">
                {validationErrors.password}
              </p>
            )}
          </div>

          <button
            type="submit"
            className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={loading || Object.keys(validationErrors).length > 0}
            aria-busy={loading}
          >
            {loading ? "Logging in..." : "Login"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AdminLogin;