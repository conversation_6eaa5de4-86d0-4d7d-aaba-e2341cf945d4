import React, { useState, useEffect } from 'react';
import Unavbar from './Unavbar';
import "./uhome.css";
import { Card, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import Footer from '../Componenets/Footer';
import axios from 'axios';

// BookCard component for displaying individual books

const BookCard = ({ book }) => (
  <Card style={{ width: '18rem', marginRight: "40px", marginBottom: "30px" }}>
    <Link to={`/book/${book.slug}`}>
      <Card.Img variant="top" src={book.image} alt={book.title} style={{ height: "350px", objectFit: "cover" }} />
    </Link>
    <Card.Body>
      <Card.Title className='text-center'>{book.title}</Card.Title>
      <Card.Text className='text-center'>
        <span style={{ fontSize: "15px", color: "#555" }}>{book.author}</span><br />
        <span style={{ fontWeight: "bold", color: "#2d8659" }}>{book.price}</span>
      </Card.Text>
      <div className="d-flex justify-content-center">
        <Link to={`/book/${book.slug}`}>
          <Button variant="primary" size="sm">View Details</Button>
        </Link>
        <Button variant="success" size="sm" style={{ marginLeft: "10px" }}>Add to Cart</Button>
      </div>
    </Card.Body>
  </Card>
);

const Uhome = () => {
  const [regularBooks, setRegularBooks] = useState([]);
  const [topRecommendations, setTopRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        // Fetch regular books
        const regularResponse = await axios.get('http://localhost:4000/books/regular');
        const regularBooksData = regularResponse.data.map(book => ({
          ...book,
          price: `₹${book.price}`,
          image: book.itemImage ? `/${book.itemImage}` : '/default_cover.svg',
          slug: book._id
        }));
        setRegularBooks(regularBooksData);

        // Fetch top recommendations
        const recommendationsResponse = await axios.get('http://localhost:4000/books/recommendations');
        const recommendationsData = recommendationsResponse.data.map(book => ({
          ...book,
          price: `₹${book.price}`,
          image: book.itemImage ? `/${book.itemImage}` : '/default_cover.svg',
          slug: book._id
        }));
        setTopRecommendations(recommendationsData);
      } catch (error) {
        console.error('Error fetching books:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBooks();
  }, []);

  if (loading) {
    return (
      <div>
        <Unavbar />
        <div className="container mt-4 text-center">
          <h2>Loading books...</h2>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Unavbar />
      <div className="container mt-4">
        <h1 className='text-center mb-4' style={{ fontSize: "50px" }}>Best Sellers</h1>
        <div className="d-flex flex-wrap justify-content-center">
          {regularBooks.map((book, idx) => (
            <BookCard book={book} key={idx} />
          ))}
        </div>
        <h1 className='text-center my-4' style={{ fontSize: "50px" }}>Top Recommendations</h1>
        <div className="d-flex flex-wrap justify-content-center">
          {topRecommendations.map((book, idx) => (
            <BookCard book={book} key={idx} />
          ))}
        </div>
        <div className="text-center mt-5">
          <Link to="/books">
            <Button variant="outline-dark" size="lg">Browse All Books</Button>
          </Link>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Uhome;