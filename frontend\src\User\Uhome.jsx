import React, { useState, useEffect } from 'react';
import Unavbar from './Unavbar';
import "./uhome.css";
import { <PERSON>, Button, Container, Row, Col, Spinner } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import Footer from '../Componenets/Footer';
import axios from 'axios';

// BookCard component for displaying individual books

const BookCard = ({ book }) => {
  const addToCart = () => {
    try {
      const cartItems = JSON.parse(localStorage.getItem('cart') || '[]');
      const existingItem = cartItems.find(item => item._id === book._id);

      if (!existingItem) {
        cartItems.push({
          ...book,
          quantity: 1
        });
        localStorage.setItem('cart', JSON.stringify(cartItems));
        alert(`"${book.title}" added to cart!`);
      } else {
        alert('Book is already in your cart!');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart');
    }
  };

  return (
    <Card className="h-100 shadow-sm book-card" style={{ width: '18rem', marginBottom: "30px", transition: "transform 0.2s" }}>
      <div style={{ height: "300px", overflow: "hidden" }}>
        <Card.Img
          variant="top"
          src={book.image}
          alt={book.title}
          style={{ height: "100%", objectFit: "cover", transition: "transform 0.2s" }}
          onError={(e) => {
            e.target.src = '/default_cover.svg';
          }}
        />
      </div>
      <Card.Body className="d-flex flex-column">
        <Card.Title className='text-center mb-2' style={{ fontSize: "1.1rem", minHeight: "2.5rem" }}>
          {book.title}
        </Card.Title>
        <Card.Text className='text-center flex-grow-1'>
          <span style={{ fontSize: "14px", color: "#666" }}>by {book.author}</span><br />
          <span style={{ fontSize: "16px", fontWeight: "bold", color: "#8B4513" }}>{book.price}</span>
        </Card.Text>
        <div className="d-flex justify-content-center gap-2 mt-auto">
          <Link to={`/uitem/${book._id}`}>
            <Button variant="outline-primary" size="sm">View Details</Button>
          </Link>
          <Button variant="success" size="sm" onClick={addToCart}>Add to Cart</Button>
        </div>
      </Card.Body>
    </Card>
  );
};

const Uhome = () => {
  const [regularBooks, setRegularBooks] = useState([]);
  const [topRecommendations, setTopRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        // Fetch regular books
        const regularResponse = await axios.get('http://localhost:4000/books/regular');
        const regularBooksData = regularResponse.data.map(book => ({
          ...book,
          price: `₹${book.price}`,
          image: book.itemImage ? `http://localhost:4000/uploads/${book.itemImage}` : '/default_cover.svg',
          slug: book._id
        }));
        setRegularBooks(regularBooksData);

        // Fetch top recommendations
        const recommendationsResponse = await axios.get('http://localhost:4000/books/recommendations');
        const recommendationsData = recommendationsResponse.data.map(book => ({
          ...book,
          price: `₹${book.price}`,
          image: book.itemImage ? `http://localhost:4000/uploads/${book.itemImage}` : '/default_cover.svg',
          slug: book._id
        }));
        setTopRecommendations(recommendationsData);
      } catch (error) {
        console.error('Error fetching books:', error);
        // Fallback books if API fails
        const fallbackBooks = [
          {
            _id: '1',
            title: '1984',
            author: 'George Orwell',
            price: '₹279',
            image: '/1984 by George Orwell.jpeg',
            description: 'A dystopian social science fiction novel'
          },
          {
            _id: '2',
            title: 'To Kill a Mockingbird',
            author: 'Harper Lee',
            price: '₹349',
            image: '/To Kill a Mockingbird.jpeg',
            description: 'A classic of modern American literature'
          },
          {
            _id: '3',
            title: 'The Great Gatsby',
            author: 'F. Scott Fitzgerald',
            price: '₹299',
            image: '/The Great Gatsby.jpeg',
            description: 'A classic American novel'
          }
        ];

        const fallbackRecommendations = [
          {
            _id: '4',
            title: 'The Alchemist',
            author: 'Paulo Coelho',
            price: '₹259',
            image: '/The Alchemist.jpeg',
            description: 'A philosophical book about following your dreams'
          },
          {
            _id: '5',
            title: 'Atomic Habits',
            author: 'James Clear',
            price: '₹399',
            image: '/Atomic Habits.jpeg',
            description: 'An easy & proven way to build good habits'
          }
        ];

        setRegularBooks(fallbackBooks);
        setTopRecommendations(fallbackRecommendations);
      } finally {
        setLoading(false);
      }
    };

    fetchBooks();
  }, []);

  if (loading) {
    return (
      <div>
        <Unavbar />
        <Container className="mt-5 text-center">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
            <div>
              <Spinner animation="border" variant="primary" style={{ width: "3rem", height: "3rem" }} />
              <h3 className="mt-3 text-muted">Loading amazing books for you...</h3>
            </div>
          </div>
        </Container>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Unavbar />

      {/* Hero Section */}
      <div className="hero-section bg-gradient-to-r from-amber-50 to-orange-50 py-5">
        <Container>
          <Row className="align-items-center">
            <Col md={8}>
              <h1 className="display-4 fw-bold text-dark mb-3">
                Welcome to <span style={{ color: "#8B4513" }}>BookNest</span>
              </h1>
              <p className="lead text-muted mb-4">
                Discover your next favorite book from our carefully curated collection of literary treasures.
              </p>
              <Link to="/books">
                <Button variant="primary" size="lg" style={{ backgroundColor: "#8B4513", borderColor: "#8B4513" }}>
                  Explore All Books
                </Button>
              </Link>
            </Col>
            <Col md={4} className="text-center">
              <img src="/logo.svg" alt="BookNest Logo" style={{ maxWidth: "250px", height: "auto" }} />
            </Col>
          </Row>
        </Container>
      </div>

      <Container className="my-5">
        {/* Best Sellers Section */}
        <div className="mb-5">
          <h2 className='text-center mb-4' style={{ fontSize: "2.5rem", color: "#8B4513", fontWeight: "bold" }}>
            📚 Best Sellers
          </h2>
          <Row className="justify-content-center g-4">
            {regularBooks.length > 0 ? (
              regularBooks.map((book, idx) => (
                <Col key={idx} xs={12} sm={6} md={4} lg={3} className="d-flex justify-content-center">
                  <BookCard book={book} />
                </Col>
              ))
            ) : (
              <Col xs={12} className="text-center">
                <p className="text-muted">No books available at the moment.</p>
              </Col>
            )}
          </Row>
        </div>

        {/* Top Recommendations Section */}
        <div className="mb-5">
          <h2 className='text-center mb-4' style={{ fontSize: "2.5rem", color: "#8B4513", fontWeight: "bold" }}>
            ⭐ Top Recommendations
          </h2>
          <Row className="justify-content-center g-4">
            {topRecommendations.length > 0 ? (
              topRecommendations.map((book, idx) => (
                <Col key={idx} xs={12} sm={6} md={4} lg={3} className="d-flex justify-content-center">
                  <BookCard book={book} />
                </Col>
              ))
            ) : (
              <Col xs={12} className="text-center">
                <p className="text-muted">No recommendations available at the moment.</p>
              </Col>
            )}
          </Row>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-5 py-4">
          <h3 className="mb-3" style={{ color: "#8B4513" }}>Can't find what you're looking for?</h3>
          <Link to="/books">
            <Button variant="outline-primary" size="lg" style={{ borderColor: "#8B4513", color: "#8B4513" }}>
              Browse All Books
            </Button>
          </Link>
        </div>
      </Container>

      <Footer />
    </div>
  );
};

export default Uhome;