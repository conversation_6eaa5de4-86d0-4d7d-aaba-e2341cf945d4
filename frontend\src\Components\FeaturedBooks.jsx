import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaStar, FaHeart, FaShoppingCart, FaEye } from 'react-icons/fa';
import axios from 'axios';

const FeaturedBooks = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchFeaturedBooks();
  }, []);

  const fetchFeaturedBooks = async () => {
    try {
      const response = await axios.get('http://localhost:4000/getitems');
      // Get first 8 books as featured
      setBooks(response.data.slice(0, 8));
    } catch (error) {
      console.error('Error fetching featured books:', error);
      // Fallback to mock data if <PERSON> fails
      setBooks(getMockBooks());
    } finally {
      setLoading(false);
    }
  };

  const getMockBooks = () => [
    {
      _id: '1',
      title: 'The Great Gatsby',
      author: '<PERSON><PERSON>',
      price: 12.99,
      image: 'https://via.placeholder.com/200x300/8b4513/f5f5dc?text=Book+Cover',
      rating: 4.5,
      reviews: 1250
    },
    {
      _id: '2',
      title: 'To Kill a Mockingbird',
      author: 'Harper Lee',
      price: 14.99,
      image: 'https://via.placeholder.com/200x300/2c1810/d2b48c?text=Book+Cover',
      rating: 4.8,
      reviews: 2100
    },
    {
      _id: '3',
      title: '1984',
      author: 'George Orwell',
      price: 13.99,
      image: 'https://via.placeholder.com/200x300/800020/ffd700?text=Book+Cover',
      rating: 4.7,
      reviews: 1800
    },
    {
      _id: '4',
      title: 'Pride and Prejudice',
      author: 'Jane Austen',
      price: 11.99,
      image: 'https://via.placeholder.com/200x300/355e3b/f5f5dc?text=Book+Cover',
      rating: 4.6,
      reviews: 1500
    }
  ];

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={i} className="star" />);
    }

    if (hasHalfStar) {
      stars.push(<FaStar key="half" className="star" style={{ opacity: 0.5 }} />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FaStar key={`empty-${i}`} className="star empty" />);
    }

    return stars;
  };

  const addToCart = (book) => {
    try {
      const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
      const existingItem = cartItems.find(item => item._id === book._id);

      if (existingItem) {
        existingItem.quantity = (existingItem.quantity || 1) + 1;
      } else {
        cartItems.push({ ...book, quantity: 1 });
      }

      localStorage.setItem('cartItems', JSON.stringify(cartItems));
      window.dispatchEvent(new Event('cartUpdated'));
      
      // Show success message
      alert(`"${book.title}" added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart');
    }
  };



  const viewBook = (bookId) => {
    navigate(`/book/${bookId}`);
  };

  if (loading) {
    return (
      <section className="featured-books py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-12 text-amber-900 font-serif-primary">
            📚 Curator's Choice
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="book-card p-4">
                <div className="skeleton h-48 mb-4 rounded"></div>
                <div className="skeleton h-4 mb-2 rounded"></div>
                <div className="skeleton h-3 mb-2 rounded"></div>
                <div className="skeleton h-4 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="featured-books py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-12 text-amber-900 font-serif-primary fade-in">
          📚 Curator's Choice
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {books.map((book, index) => (
            <div 
              key={book._id} 
              className="book-card p-4 slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="relative mb-4">
                <img
                  src={book.image || `https://via.placeholder.com/200x300/8b4513/f5f5dc?text=${encodeURIComponent(book.title)}`}
                  alt={book.title}
                  className="book-image w-full rounded-lg"
                  onError={(e) => {
                    e.target.src = `https://via.placeholder.com/200x300/8b4513/f5f5dc?text=${encodeURIComponent(book.title)}`;
                  }}
                />
                
                {/* Quick Actions Overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 
                              transition-opacity duration-300 rounded-lg flex items-center justify-center">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => viewBook(book._id)}
                      className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
                      title="View Details"
                    >
                      <FaEye />
                    </button>

                    <button
                      onClick={() => addToCart(book)}
                      className="bg-green-600 text-white p-2 rounded-full hover:bg-green-700 transition-colors"
                      title="Add to Cart"
                    >
                      <FaShoppingCart />
                    </button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-bold text-lg text-amber-900 font-serif-secondary line-clamp-2">
                  {book.title}
                </h3>
                
                <p className="text-amber-700 font-sans text-sm">
                  by {book.author || 'Unknown Author'}
                </p>

                <div className="flex items-center space-x-2">
                  <div className="rating-stars">
                    {renderStars(book.rating || 4.0)}
                  </div>
                  <span className="text-xs text-gray-600">
                    ({book.reviews || 0} reviews)
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-green-600 font-sans">
                    ${book.price || '0.00'}
                  </span>
                  
                  <button
                    onClick={() => addToCart(book)}
                    className="bg-gradient-to-r from-amber-600 to-orange-600 text-white
                              px-4 py-2 rounded-lg text-sm font-semibold
                              hover:from-amber-700 hover:to-orange-700
                              transition-all duration-300 transform hover:scale-105"
                  >
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button
            onClick={() => navigate('/books')}
            className="bg-gradient-to-r from-amber-600 to-orange-600 text-white
                      px-8 py-3 rounded-full text-lg font-semibold
                      hover:from-amber-700 hover:to-orange-700
                      transition-all duration-300 transform hover:scale-105
                      font-sans"
          >
            View All Books
          </button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedBooks;
